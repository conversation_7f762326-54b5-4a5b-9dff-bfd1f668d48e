import express from 'express';
import multer from 'multer';
import cors from 'cors';
import path from 'path';
import fs from 'fs-extra';
import { fileURLToPath } from 'url';
import { inspectImage, parseImage, generateColorPreview, generateSimpleColorPreview, createSmartColorPalette } from './index.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const port = 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, './uploads/');
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, 'image-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({ 
  storage: storage,
  fileFilter: function (req, file, cb) {
    // Accept only PNG and JPG files
    if (file.mimetype === 'image/png' || file.mimetype === 'image/jpeg') {
      cb(null, true);
    } else {
      cb(new Error('Only PNG and JPG files are allowed!'), false);
    }
  },
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB limit
  }
});

// Ensure uploads directory exists
fs.ensureDirSync('./uploads');

// Routes
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// API endpoint to inspect image
app.post('/api/inspect', upload.single('image'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No image file uploaded' });
    }

    const imagePath = req.file.path;
    const imageName = path.parse(req.file.filename).name;
    
    // Copy uploaded file to root directory with .png extension for processing
    const processingPath = `./${imageName}.png`;
    await fs.copy(imagePath, processingPath);
    
    // Inspect the image
    const options = await inspectImage(imageName);
    
    res.json({
      success: true,
      filename: imageName,
      options: options,
      originalName: req.file.originalname
    });
    
  } catch (error) {
    console.error('Error inspecting image:', error);
    res.status(500).json({ error: 'Failed to inspect image: ' + error.message });
  }
});

// API endpoint to convert image
app.post('/api/convert', async (req, res) => {
  try {
    const { filename, step, colors, quality = 'balanced', maxSize, shuffleSeed = 0 } = req.body;

    if (!filename || !step || !colors) {
      return res.status(400).json({ error: 'Missing required parameters' });
    }

    console.log(`Converting ${filename} with ${step} colors using ${quality} quality (shuffle: ${shuffleSeed})...`);

    // Apply smart color assignment if shuffled
    const smartColors = shuffleSeed > 0 ? createSmartColorPalette(colors, shuffleSeed) : colors;

    // Convert the image with performance options
    const performanceOptions = {
      quality: quality,
      maxSize: maxSize
    };

    await parseImage(filename, step, smartColors, performanceOptions);
    
    // Read the generated SVG
    const svgPath = `./${filename}.svg`;
    const svgContent = await fs.readFile(svgPath, 'utf8');
    
    res.json({
      success: true,
      svg: svgContent,
      filename: filename
    });
    
  } catch (error) {
    console.error('Error converting image:', error);
    res.status(500).json({ error: 'Failed to convert image: ' + error.message });
  }
});

// API endpoint to generate color preview
app.post('/api/preview', async (req, res) => {
  try {
    console.log('🔍 Preview request received:', req.body);
    const { filename, step, colors, shuffleSeed = 0, method = 'simple' } = req.body;

    if (!filename || !step || !colors) {
      console.error('❌ Missing required parameters:', { filename, step, colors });
      return res.status(400).json({ error: 'Missing required parameters' });
    }

    console.log(`🎨 Generating preview: ${step} colors, method: ${method}, shuffle: ${shuffleSeed}`);
    console.log(`📁 Image file: ${filename}`);

    const startTime = Date.now();
    console.log(`⏱️ Preview generation started at ${new Date().toISOString()}`);

    // Use the vectorized method by default for exact matching with final result
    const preview = method === 'simple'
      ? await generateSimpleColorPreview(filename, step, colors, shuffleSeed)
      : await generateColorPreview(filename, step, colors, shuffleSeed);

    const generationTime = Date.now() - startTime;
    console.log(`✅ Preview generated in ${generationTime}ms`);

    // Read the preview image as base64 for display
    const previewBuffer = await fs.readFile(preview.previewPath);
    const previewBase64 = previewBuffer.toString('base64');

    // Add cache control headers to prevent caching issues
    res.set({
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    });

    res.json({
      success: true,
      preview: `data:image/png;base64,${previewBase64}`,
      shuffleSeed: shuffleSeed,
      colors: preview.colors,
      step: step,
      method: method,
      colorAssignment: preview.colorAssignment,
      timestamp: Date.now() // Cache busting
    });

    // Clean up old preview files to prevent disk space issues
    setTimeout(async () => {
      try {
        await fs.remove(preview.previewPath);
      } catch (err) {
        // Ignore cleanup errors
      }
    }, 30000); // Clean up after 30 seconds

  } catch (error) {
    console.error('❌ Error generating preview:', error);
    console.error('Error stack:', error.stack);
    res.status(500).json({ error: 'Failed to generate preview: ' + error.message });
  }
});

// API endpoint to download SVG
app.get('/api/download/:filename', (req, res) => {
  try {
    const filename = req.params.filename;
    const svgPath = `./${filename}.svg`;

    if (fs.existsSync(svgPath)) {
      res.download(svgPath, `${filename}.svg`);
    } else {
      res.status(404).json({ error: 'SVG file not found' });
    }
  } catch (error) {
    console.error('Error downloading file:', error);
    res.status(500).json({ error: 'Failed to download file' });
  }
});

app.listen(port, () => {
  console.log(`🚀 Vectorizer web app running at http://localhost:${port}`);
  console.log(`📁 Upload images and convert them to SVG in your browser!`);
});
