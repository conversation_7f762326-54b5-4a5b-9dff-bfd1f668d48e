class VectorizerApp {
    constructor() {
        this.currentFile = null;
        this.currentOptions = null;
        this.selectedOption = null;
        this.currentFilename = null;
        this.shuffleCount = 0;
        
        this.initializeElements();
        this.bindEvents();
    }
    
    initializeElements() {
        this.uploadArea = document.getElementById('uploadArea');
        this.fileInput = document.getElementById('fileInput');
        this.loading = document.getElementById('loading');
        this.optionsSection = document.getElementById('optionsSection');
        this.optionsContainer = document.getElementById('optionsContainer');
        this.previewBtn = document.getElementById('previewBtn');
        this.previewSection = document.getElementById('previewSection');
        this.originalImagePreview = document.getElementById('originalImagePreview');
        this.colorPreview = document.getElementById('colorPreview');
        this.shuffleBtn = document.getElementById('shuffleBtn');
        this.shuffleCountSpan = document.getElementById('shuffleCount');
        this.convertBtn = document.getElementById('convertBtn');
        this.backToOptionsBtn = document.getElementById('backToOptionsBtn');
        this.resultSection = document.getElementById('resultSection');
        this.originalPreview = document.getElementById('originalPreview');
        this.svgPreview = document.getElementById('svgPreview');
        this.downloadBtn = document.getElementById('downloadBtn');
        this.newImageBtn = document.getElementById('newImageBtn');
    }
    
    bindEvents() {
        // Upload area events
        this.uploadArea.addEventListener('click', () => this.fileInput.click());
        this.uploadArea.addEventListener('dragover', this.handleDragOver.bind(this));
        this.uploadArea.addEventListener('dragleave', this.handleDragLeave.bind(this));
        this.uploadArea.addEventListener('drop', this.handleDrop.bind(this));
        
        // File input change
        this.fileInput.addEventListener('change', this.handleFileSelect.bind(this));

        // Preview button
        this.previewBtn.addEventListener('click', (e) => {
            e.preventDefault();
            console.log('🔍 Preview button clicked');
            this.showPreview();
        });

        // Shuffle button
        this.shuffleBtn.addEventListener('click', this.shuffleColors.bind(this));

        // Back to options button
        this.backToOptionsBtn.addEventListener('click', this.backToOptions.bind(this));

        // Convert button
        this.convertBtn.addEventListener('click', (e) => {
            e.preventDefault();
            console.log('🔄 Convert button clicked');
            this.convertImage();
        });
        
        // Download button
        this.downloadBtn.addEventListener('click', this.downloadSVG.bind(this));
        
        // New image button
        this.newImageBtn.addEventListener('click', this.resetApp.bind(this));
    }
    
    handleDragOver(e) {
        e.preventDefault();
        this.uploadArea.classList.add('dragover');
    }
    
    handleDragLeave(e) {
        e.preventDefault();
        this.uploadArea.classList.remove('dragover');
    }
    
    handleDrop(e) {
        e.preventDefault();
        this.uploadArea.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            this.processFile(files[0]);
        }
    }
    
    handleFileSelect(e) {
        const file = e.target.files[0];
        if (file) {
            this.processFile(file);
        }
    }
    
    async processFile(file) {
        // Validate file type
        if (!file.type.match(/^image\/(png|jpeg)$/)) {
            this.showError('Please select a PNG or JPG image file.');
            return;
        }
        
        // Validate file size (10MB)
        if (file.size > 10 * 1024 * 1024) {
            this.showError('File size must be less than 10MB.');
            return;
        }
        
        this.currentFile = file;
        this.showLoading(true);
        this.hideError();
        
        try {
            const formData = new FormData();
            formData.append('image', file);
            
            const response = await fetch('/api/inspect', {
                method: 'POST',
                body: formData
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.currentOptions = result.options;
                this.currentFilename = result.filename;
                this.showOptions(result.options);
                this.showOriginalPreview(file);
            } else {
                this.showError(result.error || 'Failed to process image');
            }
        } catch (error) {
            this.showError('Network error: ' + error.message);
        } finally {
            this.showLoading(false);
        }
    }
    
    showOptions(options) {
        this.optionsContainer.innerHTML = '';

        // Add quality settings section
        const qualitySection = document.createElement('div');
        qualitySection.innerHTML = `
            <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                <h4 style="margin-bottom: 15px;">⚙️ Performance Settings</h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: 500;">Quality Mode:</label>
                        <select id="qualitySelect" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 5px;">
                            <option value="auto">🤖 Auto (Recommended)</option>
                            <option value="fast">⚡ Fast (Lower quality, faster)</option>
                            <option value="balanced">⚖️ Balanced (Good quality & speed)</option>
                            <option value="high">💎 High (Best quality, slower)</option>
                        </select>
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: 500;">Max Image Size:</label>
                        <select id="maxSizeSelect" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 5px;">
                            <option value="auto">🤖 Auto (Recommended)</option>
                            <option value="600">📱 Small (600px - Fastest)</option>
                            <option value="1200">💻 Medium (1200px - Balanced)</option>
                            <option value="2400">🖥️ Large (2400px - Best quality)</option>
                            <option value="">🔓 Original (No resize)</option>
                        </select>
                    </div>
                </div>
                <p style="margin-top: 10px; color: #666; font-size: 0.9em;">
                    💡 Tip: For 8+ colors, we recommend Fast mode and smaller sizes for better performance.
                </p>
            </div>
        `;
        this.optionsContainer.appendChild(qualitySection);

        options.forEach((option, index) => {
            const optionCard = document.createElement('div');
            optionCard.className = 'option-card';

            // Create description based on step count
            let description = '';
            switch(option.step) {
                case 1:
                    description = 'Simple silhouette - single color, best for logos and icons';
                    break;
                case 2:
                    description = 'Two-tone design - good for simple illustrations';
                    break;
                case 3:
                    description = 'Three colors - balanced detail and simplicity';
                    break;
                case 4:
                    description = 'Four colors - good detail with manageable complexity';
                    break;
                case 5:
                    description = 'Five colors - enhanced detail and color variation';
                    break;
                case 6:
                    description = 'Six colors - rich detail with good color depth';
                    break;
                case 12:
                    description = 'Twelve colors - maximum detail and color fidelity';
                    break;
                default:
                    description = `${option.step} colors - custom vectorization`;
            }

            // Add performance info
            const performanceInfo = option.estimatedTime ?
                `<div style="background: ${option.step >= 8 ? '#fff3cd' : option.step >= 5 ? '#d1ecf1' : '#d4edda'};
                     padding: 8px; border-radius: 5px; margin-top: 10px; font-size: 0.85em;">
                    ⏱️ ${option.estimatedTime} | 🎯 ${option.recommendedQuality} quality
                    ${option.maxSize ? ` | 📏 Max ${option.maxSize}px` : ''}
                </div>` : '';

            optionCard.innerHTML = `
                <h4>Option ${index + 1}: ${option.step} Color${option.step > 1 ? 's' : ''}</h4>
                <p style="color: #666; font-size: 0.9em; margin-bottom: 10px;">${description}</p>
                <div style="margin-top: 10px;">
                    ${option.colors.slice(0, 8).map(color => `<span style="display: inline-block; width: 20px; height: 20px; background: ${color}; border-radius: 50%; margin-right: 5px; border: 1px solid #ddd;"></span>`).join('')}
                    ${option.colors.length > 8 ? `<span style="color: #999; font-size: 0.9em;">+${option.colors.length - 8} more</span>` : ''}
                </div>
                ${performanceInfo}
            `;
            
            optionCard.addEventListener('click', () => {
                // Remove previous selection
                document.querySelectorAll('.option-card').forEach(card => {
                    card.classList.remove('selected');
                });
                
                // Select this option
                optionCard.classList.add('selected');
                this.selectedOption = option;
                this.previewBtn.disabled = false;
            });
            
            this.optionsContainer.appendChild(optionCard);
        });
        
        this.optionsSection.style.display = 'block';
    }
    
    showOriginalPreview(file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            this.originalPreview.src = e.target.result;
        };
        reader.readAsDataURL(file);
    }

    async showPreview() {
        console.log('🔍 showPreview called');
        console.log('Selected option:', this.selectedOption);
        console.log('Current filename:', this.currentFilename);

        if (!this.selectedOption || !this.currentFilename) {
            console.error('❌ Missing required data for preview');
            this.showError('Please select an option first.');
            return;
        }

        console.log('🔍 Starting preview generation...');
        this.showLoading(true);
        this.shuffleCount = 0;
        this.shuffleCountSpan.textContent = this.shuffleCount;

        try {
            // Show original image in preview
            const reader = new FileReader();
            reader.onload = (e) => {
                this.originalImagePreview.src = e.target.result;
            };
            reader.readAsDataURL(this.currentFile);

            // Generate color preview
            await this.generateColorPreview(0);

            // Show preview section
            this.optionsSection.style.display = 'none';
            this.previewSection.style.display = 'block';
            this.convertBtn.disabled = false;

            // Scroll to preview
            this.previewSection.scrollIntoView({ behavior: 'smooth' });

        } catch (error) {
            this.showError('Failed to generate preview: ' + error.message);
        } finally {
            this.showLoading(false);
        }
    }

    async generateColorPreview(shuffleSeed) {
        try {
            // Show preview loading state
            this.colorPreview.style.opacity = '0.5';
            this.colorPreview.style.filter = 'blur(2px)';

            console.log(`🚀 Generating ULTRA-FAST preview: ${this.selectedOption.step} colors`);

            // Add timeout for preview requests (30 seconds max)
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 30000);

            const response = await fetch('/api/preview', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Cache-Control': 'no-cache'
                },
                body: JSON.stringify({
                    filename: this.currentFilename,
                    step: this.selectedOption.step,
                    colors: this.selectedOption.colors,
                    shuffleSeed: shuffleSeed,
                    method: 'vectorized'  // Use exact vectorization method
                }),
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            const result = await response.json();

            if (result.success) {
                console.log('🔍 Debug: Preview result received');
                console.log('Preview data length:', result.preview ? result.preview.length : 'null');
                console.log('Preview starts with:', result.preview ? result.preview.substring(0, 50) : 'null');
                console.log('Color preview element:', this.colorPreview);

                // Set up event handlers BEFORE setting src to avoid race conditions
                this.colorPreview.onload = () => {
                    console.log('🔍 Preview image loaded successfully');
                    this.colorPreview.style.opacity = '1';
                    this.colorPreview.style.filter = 'none';
                };

                this.colorPreview.onerror = (e) => {
                    console.error('❌ Preview image failed to load:', e);
                    console.error('Image src length:', this.colorPreview.src.length);
                    console.error('Image src start:', this.colorPreview.src.substring(0, 100));
                    // Don't reset opacity/filter on error - let it stay in loading state
                    // The next successful load will clear it
                };

                // Force reload by clearing src first
                this.colorPreview.src = '';

                // Set the new preview image after a brief delay
                setTimeout(() => {
                    console.log('🔍 Setting preview src...');
                    this.colorPreview.src = result.preview;
                    console.log('🔍 Preview src set to:', this.colorPreview.src.substring(0, 50));
                }, 50); // Increased delay to ensure handlers are set

                // Update color assignment display
                if (result.colorAssignment) {
                    this.updateColorAssignmentDisplay(result.colorAssignment);
                }

                console.log(`✅ Preview updated: ${result.step} colors, shuffle ${result.shuffleSeed}`);
                console.log(`Colors: ${result.colors.join(', ')}`);
            } else {
                this.showError(result.error || 'Failed to generate preview');
                // Remove loading state on error
                this.colorPreview.style.opacity = '1';
                this.colorPreview.style.filter = 'none';
            }
        } catch (error) {
            this.showError('Network error: ' + error.message);
        }
    }

    updateColorAssignmentDisplay(colorAssignment) {
        const container = document.getElementById('currentColorAssignment');
        if (!container || !colorAssignment) return;

        const { darkest, lightest, middle } = colorAssignment;

        let html = '<div style="display: flex; flex-wrap: wrap; gap: 10px; justify-content: center;">';

        // Show darkest color (fixed)
        html += `<div style="display: flex; align-items: center; gap: 5px;">
            <div style="width: 20px; height: 20px; background: ${darkest}; border-radius: 50%; border: 2px solid #ccc;"></div>
            <span>🖤 Darkest (Fixed)</span>
        </div>`;

        // Show middle colors (shuffleable)
        if (middle && middle.length > 0) {
            middle.forEach((color, index) => {
                html += `<div style="display: flex; align-items: center; gap: 5px;">
                    <div style="width: 20px; height: 20px; background: ${color}; border-radius: 50%; border: 2px solid #ccc;"></div>
                    <span>🎨 Middle ${index + 1}</span>
                </div>`;
            });
        }

        // Show lightest color (fixed)
        html += `<div style="display: flex; align-items: center; gap: 5px;">
            <div style="width: 20px; height: 20px; background: ${lightest}; border-radius: 50%; border: 2px solid #ccc;"></div>
            <span>🤍 Lightest (Fixed)</span>
        </div>`;

        html += '</div>';
        container.innerHTML = html;
    }

    async shuffleColors() {
        this.shuffleCount++;
        this.shuffleCountSpan.textContent = this.shuffleCount;

        this.showLoading(true);
        try {
            await this.generateColorPreview(this.shuffleCount);
        } finally {
            this.showLoading(false);
        }
    }

    backToOptions() {
        this.previewSection.style.display = 'none';
        this.optionsSection.style.display = 'block';
        this.convertBtn.disabled = true;

        // Scroll to options
        this.optionsSection.scrollIntoView({ behavior: 'smooth' });
    }
    
    async convertImage() {
        console.log('🔄 convertImage called');
        if (!this.selectedOption || !this.currentFilename) {
            this.showError('Please select an option first.');
            return;
        }

        // Get quality settings
        const qualitySelect = document.getElementById('qualitySelect');
        const maxSizeSelect = document.getElementById('maxSizeSelect');

        let quality = qualitySelect.value;
        let maxSize = maxSizeSelect.value;

        // Auto mode uses recommended settings from the option
        if (quality === 'auto') {
            quality = this.selectedOption.recommendedQuality || 'balanced';
        }
        if (maxSize === 'auto') {
            maxSize = this.selectedOption.maxSize || null;
        }
        if (maxSize === '') {
            maxSize = null; // No resize
        } else if (maxSize) {
            maxSize = parseInt(maxSize);
        }

        this.showLoading(true);
        this.convertBtn.disabled = true;

        // Show performance warning for complex operations
        if (this.selectedOption.step >= 8 && quality === 'high') {
            this.showSuccess('⏳ Processing complex image with high quality. This may take several minutes...');
        }

        try {
            const response = await fetch('/api/convert', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    filename: this.currentFilename,
                    step: this.selectedOption.step,
                    colors: this.selectedOption.colors,
                    quality: quality,
                    maxSize: maxSize,
                    shuffleSeed: this.shuffleCount || 0  // Use the current shuffle state
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showResult(result.svg);
                this.showSuccess('Image converted successfully!');
            } else {
                this.showError(result.error || 'Failed to convert image');
                this.convertBtn.disabled = false;
            }
        } catch (error) {
            this.showError('Network error: ' + error.message);
            this.convertBtn.disabled = false;
        } finally {
            this.showLoading(false);
        }
    }
    
    showResult(svgContent) {
        this.svgPreview.innerHTML = svgContent;
        this.resultSection.style.display = 'block';
        
        // Scroll to result
        this.resultSection.scrollIntoView({ behavior: 'smooth' });
    }
    
    async downloadSVG() {
        if (!this.currentFilename) return;
        
        try {
            const response = await fetch(`/api/download/${this.currentFilename}`);
            const blob = await response.blob();
            
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${this.currentFilename}.svg`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        } catch (error) {
            this.showError('Failed to download file: ' + error.message);
        }
    }
    
    resetApp() {
        this.currentFile = null;
        this.currentOptions = null;
        this.selectedOption = null;
        this.currentFilename = null;
        this.shuffleCount = 0;

        this.fileInput.value = '';
        this.optionsSection.style.display = 'none';
        this.previewSection.style.display = 'none';
        this.resultSection.style.display = 'none';
        this.previewBtn.disabled = true;
        this.convertBtn.disabled = true;
        this.hideError();
        this.hideSuccess();
        
        // Scroll to top
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }
    
    showLoading(show) {
        this.loading.style.display = show ? 'block' : 'none';
    }
    
    showError(message) {
        this.hideError();
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error';
        errorDiv.textContent = message;
        this.uploadArea.parentNode.insertBefore(errorDiv, this.uploadArea.nextSibling);
    }
    
    showSuccess(message) {
        this.hideSuccess();
        const successDiv = document.createElement('div');
        successDiv.className = 'success';
        successDiv.textContent = message;
        this.uploadArea.parentNode.insertBefore(successDiv, this.uploadArea.nextSibling);
    }
    
    hideError() {
        const errorDiv = document.querySelector('.error');
        if (errorDiv) {
            errorDiv.remove();
        }
    }
    
    hideSuccess() {
        const successDiv = document.querySelector('.success');
        if (successDiv) {
            successDiv.remove();
        }
    }
}

// Initialize the app when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new VectorizerApp();
});
