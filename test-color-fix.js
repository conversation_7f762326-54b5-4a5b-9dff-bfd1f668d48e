const { inspectImage, parseImage } = require('./index_local.js');

async function testColorFix() {
    console.log('🧪 Testing Color Quantization Fix...\n');
    
    // Test with a sample image (you'll need to provide one)
    const imageName = 'test-image'; // Assumes test-image.png exists
    
    try {
        console.log('📊 Inspecting image for color options...');
        const options = await inspectImage(imageName);
        
        console.log(`Found ${options.length} vectorization options:`);
        options.forEach((option, i) => {
            console.log(`  ${i+1}. ${option.step} colors: ${option.colors.slice(0, 5).join(', ')}${option.colors.length > 5 ? '...' : ''}`);
        });
        
        // Test 5-color option if available
        const option5 = options.find(opt => opt.step === 5);
        if (option5) {
            console.log(`\n🎨 Testing 5-color conversion...`);
            console.log(`Colors to use: ${option5.colors}`);
            
            const startTime = Date.now();
            await parseImage(imageName, option5.step, option5.colors);
            const duration = (Date.now() - startTime) / 1000;
            
            console.log(`✅ 5-color conversion completed in ${duration.toFixed(1)} seconds!`);
            console.log(`📁 Check ${imageName}.svg for the result`);
        } else {
            console.log('❌ No 5-color option found for this image');
        }
        
    } catch (error) {
        console.error('❌ Error during test:', error.message);
    }
}

// Run the test
testColorFix();
