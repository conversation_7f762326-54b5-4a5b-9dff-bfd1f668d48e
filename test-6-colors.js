import { inspectImage, parseImage } from './index.js';

async function test6Colors() {
  try {
    const imageName = 'test';
    
    console.log(`🔍 Testing 6-color conversion for ${imageName}.png...`);
    
    // Get all options
    const options = await inspectImage(imageName);
    
    // Find the 6-color option
    const option6 = options.find(opt => opt.step === 6);
    
    if (!option6) {
      console.log('❌ 6-color option not available for this image.');
      return;
    }
    
    console.log(`\n🎨 6-Color Option Details:`);
    console.log(`Colors: ${option6.colors}`);
    
    console.log(`\n⚡ Starting 6-color conversion...`);
    const startTime = Date.now();
    
    // Use performance options with image resizing
    const performanceOptions = {
      quality: 'balanced',
      maxSize: 1000 // Smaller size for faster processing
    };
    
    await parseImage(imageName, option6.step, option6.colors, performanceOptions);
    
    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;
    
    console.log(`\n✅ 6-color conversion completed in ${duration.toFixed(1)} seconds!`);
    console.log(`📁 Output: ${imageName}.svg`);
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

test6Colors();
