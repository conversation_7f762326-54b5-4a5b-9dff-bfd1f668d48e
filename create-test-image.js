import sharp from 'sharp';

// Create a simple test image
async function createTestImage() {
  try {
    // Create a simple colored rectangle as a test image
    const width = 200;
    const height = 200;
    
    // Create a simple SVG that we'll convert to PNG for testing
    const svgBuffer = Buffer.from(`
      <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
        <rect width="100%" height="100%" fill="#ffffff"/>
        <circle cx="100" cy="100" r="60" fill="#ff6b6b"/>
        <rect x="70" y="70" width="60" height="60" fill="#4ecdc4"/>
      </svg>
    `);
    
    // Convert SVG to PNG
    await sharp(svgBuffer)
      .png()
      .toFile('test.png');
      
    console.log('✅ Created test.png - a simple image with a red circle and blue square');
    console.log('📝 You can now run: node test-vectorizer.js');
    
  } catch (error) {
    console.error('❌ Error creating test image:', error.message);
  }
}

createTestImage();
