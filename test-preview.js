import { generateSimpleColorPreview, inspectImage, createSmartColorPalette } from './index.js';
import fs from 'fs-extra';

async function testPreview() {
    console.log('🧪 Testing Color Preview Functionality...\n');
    
    // You'll need to place a test image as 'test-image.png'
    const imageName = 'test-image';
    
    try {
        // Check if test image exists
        if (!await fs.pathExists(`./${imageName}.png`)) {
            console.log('❌ Please place a test image as test-image.png in the root directory');
            return;
        }
        
        console.log('📊 Inspecting image...');
        const options = await inspectImage(imageName);
        
        // Test with 5 colors if available
        const option5 = options.find(opt => opt.step === 5);
        if (!option5) {
            console.log('❌ No 5-color option found');
            return;
        }
        
        console.log(`\n🎨 Testing smart color assignment with 5 colors:`);
        console.log(`Original colors: ${option5.colors.join(', ')}`);

        // Test smart color palette creation
        console.log('\n🧠 Testing smart color assignment...');
        const smartPalette0 = createSmartColorPalette(option5.colors, 0);
        const smartPalette1 = createSmartColorPalette(option5.colors, 1);
        const smartPalette2 = createSmartColorPalette(option5.colors, 2);

        console.log(`Smart palette (no shuffle): ${smartPalette0.join(', ')}`);
        console.log(`Smart palette (shuffle 1): ${smartPalette1.join(', ')}`);
        console.log(`Smart palette (shuffle 2): ${smartPalette2.join(', ')}`);

        // Generate original preview
        console.log('\n🔍 Generating original smart preview...');
        const preview1 = await generateSimpleColorPreview(imageName, 5, option5.colors, 0);
        console.log(`✅ Original preview saved: ${preview1.previewPath}`);
        console.log(`Color assignment:`, preview1.colorAssignment);

        // Generate shuffled preview
        console.log('\n🎲 Generating shuffled smart preview...');
        const preview2 = await generateSimpleColorPreview(imageName, 5, option5.colors, 1);
        console.log(`✅ Shuffled preview saved: ${preview2.previewPath}`);
        console.log(`Color assignment:`, preview2.colorAssignment);

        // Generate another shuffle
        console.log('\n🎲 Generating another shuffled smart preview...');
        const preview3 = await generateSimpleColorPreview(imageName, 5, option5.colors, 2);
        console.log(`✅ Second shuffle saved: ${preview3.previewPath}`);
        console.log(`Color assignment:`, preview3.colorAssignment);

        console.log('\n🎯 Smart color assignment test completed!');
        console.log('✅ Darkest and lightest colors should stay fixed');
        console.log('✅ Only middle colors should shuffle between previews');
        console.log('✅ Each preview shows exactly 5 colors with smart placement');
        
    } catch (error) {
        console.error('❌ Error during preview test:', error.message);
        console.error(error.stack);
    }
}

// Run the test
testPreview();
