import { inspectImage, parseImage } from './index.js';

async function test12Colors() {
  try {
    const imageName = 'test';
    
    console.log(`🔍 Analyzing ${imageName}.png for 12-color conversion...`);
    
    // Get all options
    const options = await inspectImage(imageName);
    
    // Find the 12-color option
    const option12 = options.find(opt => opt.step === 12);
    
    if (!option12) {
      console.log('❌ 12-color option not available for this image.');
      return;
    }
    
    console.log(`\n🎨 12-Color Option Details:`);
    console.log(`Colors: ${option12.colors.length}`);
    console.log(`Recommended quality: ${option12.recommendedQuality}`);
    console.log(`Estimated time: ${option12.estimatedTime}`);
    console.log(`Max size: ${option12.maxSize || 'Original'}`);
    
    console.log(`\n⚡ Starting FAST 12-color conversion...`);
    const startTime = Date.now();
    
    // Use the recommended performance settings (fast mode)
    const performanceOptions = {
      quality: option12.recommendedQuality, // 'fast'
      maxSize: option12.maxSize // 800px
    };
    
    await parseImage(imageName, option12.step, option12.colors, performanceOptions);
    
    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;
    
    console.log(`\n✅ 12-color conversion completed in ${duration.toFixed(1)} seconds!`);
    console.log(`📁 Output: ${imageName}.svg`);
    console.log(`\n🚀 Performance optimizations applied:`);
    console.log(`   • Quality: ${performanceOptions.quality}`);
    console.log(`   • Max size: ${performanceOptions.maxSize}px`);
    console.log(`   • Optimized Potrace settings for speed`);
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

test12Colors();
