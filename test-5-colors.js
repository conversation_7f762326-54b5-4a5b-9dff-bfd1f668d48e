import { inspectImage, parseImage } from './index.js';

async function test5Colors() {
  try {
    const imageName = 'test';
    
    console.log(`🔍 Testing 5-color conversion for ${imageName}.png...`);
    
    // Get all options
    const options = await inspectImage(imageName);
    
    // Find the 5-color option
    const option5 = options.find(opt => opt.step === 5);
    
    if (!option5) {
      console.log('❌ 5-color option not available for this image.');
      return;
    }
    
    console.log(`\n🎨 5-Color Option Details:`);
    console.log(`Colors: ${option5.colors}`);
    console.log(`Recommended quality: ${option5.recommendedQuality}`);
    
    console.log(`\n⚡ Starting 5-color conversion with fixed settings...`);
    const startTime = Date.now();
    
    // Use simple performance options (just image resizing, no complex Potrace params)
    const performanceOptions = {
      quality: 'balanced', // Use balanced instead of fast to avoid issues
      maxSize: 1200 // Resize for speed
    };
    
    await parseImage(imageName, option5.step, option5.colors, performanceOptions);
    
    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;
    
    console.log(`\n✅ 5-color conversion completed in ${duration.toFixed(1)} seconds!`);
    console.log(`📁 Output: ${imageName}.svg`);
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

test5Colors();
