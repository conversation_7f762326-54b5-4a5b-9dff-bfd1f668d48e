<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vectorizer - Convert Images to SVG</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #ff6b6b, #4ecdc4);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px;
        }
        
        .upload-area {
            border: 3px dashed #ddd;
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            margin-bottom: 30px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .upload-area:hover {
            border-color: #4ecdc4;
            background: #f8f9fa;
        }
        
        .upload-area.dragover {
            border-color: #ff6b6b;
            background: #fff5f5;
        }
        
        .upload-icon {
            font-size: 4em;
            color: #ddd;
            margin-bottom: 20px;
        }
        
        .upload-text {
            font-size: 1.3em;
            color: #666;
            margin-bottom: 15px;
        }
        
        .file-input {
            display: none;
        }
        
        .btn {
            background: linear-gradient(135deg, #ff6b6b, #4ecdc4);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .options-section {
            display: none;
            margin-top: 30px;
            padding: 30px;
            background: #f8f9fa;
            border-radius: 15px;
        }
        
        .option-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 10px 0;
            border: 2px solid transparent;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .option-card:hover {
            border-color: #4ecdc4;
            transform: translateY(-2px);
        }
        
        .option-card.selected {
            border-color: #ff6b6b;
            background: #fff5f5;
        }
        
        .result-section {
            display: none;
            margin-top: 30px;
            padding: 30px;
            background: #f8f9fa;
            border-radius: 15px;
        }
        
        .preview-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-top: 20px;
        }
        
        .preview-box {
            background: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
        }
        
        .preview-box h3 {
            margin-bottom: 15px;
            color: #333;
        }
        
        .preview-image {
            max-width: 100%;
            max-height: 300px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4ecdc4;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .error {
            background: #ffe6e6;
            color: #d63031;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #d63031;
        }
        
        .success {
            background: #e6ffe6;
            color: #00b894;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #00b894;
        }
        
        @media (max-width: 768px) {
            .preview-container {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 Vectorizer</h1>
            <p>Convert your raster images (PNG/JPG) to scalable vector graphics (SVG)</p>
        </div>
        
        <div class="content">
            <!-- Upload Section -->
            <div class="upload-area" id="uploadArea">
                <div class="upload-icon">📁</div>
                <div class="upload-text">Drop your image here or click to browse</div>
                <div style="color: #999; font-size: 0.9em;">Supports PNG and JPG files (max 10MB)</div>
                <input type="file" id="fileInput" class="file-input" accept="image/png,image/jpeg">
            </div>
            
            <!-- Loading -->
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <div>Processing your image...</div>
            </div>
            
            <!-- Options Section -->
            <div class="options-section" id="optionsSection">
                <h2>🎯 Choose Vectorization Options</h2>
                <p style="margin-bottom: 20px; color: #666;">Select the best option for your image:</p>
                <div id="optionsContainer"></div>
                <button class="btn" id="previewBtn" disabled>🔍 Preview Colors</button>
            </div>

            <!-- Preview Section -->
            <div class="options-section" id="previewSection" style="display: none;">
                <h2>🎨 Smart Color Preview</h2>
                <p style="margin-bottom: 15px; color: #666;">Preview with intelligent color placement:</p>
                <div id="colorAssignmentInfo" style="background: #f8f9fa; padding: 15px; border-radius: 10px; margin-bottom: 20px; font-size: 0.9em;">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;">
                        <div>🤍 <strong>Lightest (Fixed):</strong> Eyes, teeth, highlights</div>
                        <div>🖤 <strong>Darkest (Fixed):</strong> Shadows, mouth interior</div>
                        <div>🎨 <strong>Middle Colors:</strong> Shuffleable for skin, clothing, etc.</div>
                    </div>
                </div>

                <div class="preview-container">
                    <div class="preview-box">
                        <h3>Original Image</h3>
                        <img id="originalImagePreview" class="preview-image" alt="Original">
                    </div>
                    <div class="preview-box">
                        <h3>Color Preview</h3>
                        <p style="font-size: 0.85em; color: #666; margin-bottom: 10px;">
                            ⚡⚡ ULTRA-FAST preview (200px, high tolerance)
                        </p>
                        <img id="colorPreview" class="preview-image" alt="Color Preview">
                        <div style="margin-top: 15px;">
                            <button class="btn" id="shuffleBtn">🎲 Shuffle Middle Colors</button>
                            <div style="margin-top: 10px; font-size: 0.9em; color: #666;">
                                Shuffle #<span id="shuffleCount">0</span>
                            </div>
                            <div id="currentColorAssignment" style="margin-top: 10px; font-size: 0.85em; color: #666;">
                                <!-- Color assignment info will be shown here -->
                            </div>
                        </div>
                    </div>
                </div>

                <div style="text-align: center; margin-top: 20px;">
                    <button class="btn" id="convertBtn" disabled>✨ Convert to SVG</button>
                    <button class="btn" id="backToOptionsBtn" style="background: #6c757d;">← Back to Options</button>
                </div>
            </div>
            
            <!-- Result Section -->
            <div class="result-section" id="resultSection">
                <h2>✨ Conversion Complete!</h2>
                <div class="preview-container">
                    <div class="preview-box">
                        <h3>Original Image</h3>
                        <img id="originalPreview" class="preview-image" alt="Original">
                    </div>
                    <div class="preview-box">
                        <h3>Vector Result</h3>
                        <div id="svgPreview"></div>
                    </div>
                </div>
                <div style="text-align: center; margin-top: 20px;">
                    <button class="btn" id="downloadBtn">📥 Download SVG</button>
                    <button class="btn" id="newImageBtn">🔄 Convert Another Image</button>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
