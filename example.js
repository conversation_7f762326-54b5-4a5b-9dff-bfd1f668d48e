import { inspectImage, parseImage } from './index.js';

async function convertImage() {
  const imageName = 'your-image'; // without .png extension
  
  // First, inspect the image to get possible options
  const options = await inspectImage(imageName);
  console.log('Available options:', options);
  
  // Then use one of the options to convert the image
  // options[0] will be the first (usually best) option
  if (options.length > 0) {
    await parseImage(imageName, options[0].step, options[0].colors);
    console.log(`Converted ${imageName}.png to ${imageName}.svg`);
  }
}

// Usage: Place your PNG file in the same directory and call:
// convertImage();
