import { inspectImage, parseImage } from './index.js';

async function testVectorizer() {
  try {
    // Replace 'test' with your image name (without .png extension)
    const imageName = 'test';
    
    console.log(`Analyzing ${imageName}.png...`);
    
    // First, inspect the image to get possible options
    const options = await inspectImage(imageName);
    console.log('Available vectorization options:', options);
    
    if (options.length > 0) {
      const option = options[0];
      console.log(`Converting with option: step=${option.step}, colors=${option.colors}`);
      console.log(`Recommended quality: ${option.recommendedQuality}, estimated time: ${option.estimatedTime}`);

      // Use recommended performance settings for the option
      const performanceOptions = {
        quality: option.recommendedQuality,
        maxSize: option.maxSize
      };

      // Convert using the first option with performance optimization
      await parseImage(imageName, option.step, option.colors, performanceOptions);

      console.log(`✅ Successfully converted ${imageName}.png to ${imageName}.svg`);
    } else {
      console.log('❌ No suitable vectorization options found for this image.');
    }
  } catch (error) {
    console.error('❌ Error:', error.message);
    console.log('\n📝 Make sure:');
    console.log('1. Your PNG file exists in this directory');
    console.log('2. The filename matches what you specified');
    console.log('3. The image is a valid PNG file');
  }
}

// Run the test
testVectorizer();
