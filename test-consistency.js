import { generateColorPreview, parseImage, inspectImage } from './index.js';
import fs from 'fs-extra';
import sharp from 'sharp';

async function testPreviewConsistency() {
    console.log('🔍 Testing Preview-to-Final Consistency...\n');
    
    const imageName = 'test-image';
    
    try {
        // Check if test image exists
        if (!await fs.pathExists(`./${imageName}.png`)) {
            console.log('❌ Please place a test image as test-image.png in the root directory');
            return;
        }
        
        console.log('📊 Inspecting image...');
        const options = await inspectImage(imageName);
        
        // Test with 5 colors if available
        const option5 = options.find(opt => opt.step === 5);
        if (!option5) {
            console.log('❌ No 5-color option found');
            return;
        }
        
        console.log(`\n🎨 Testing with 5 colors:`);
        console.log(`Colors: ${option5.colors.join(', ')}`);
        
        // Generate preview using exact vectorization method
        console.log('\n🔍 Generating preview (using exact vectorization)...');
        const preview = await generateColorPreview(imageName, 5, option5.colors, 1);
        console.log(`✅ Preview generated: ${preview.previewPath}`);
        console.log(`Preview colors: ${preview.colors.join(', ')}`);
        
        // Generate final result using same parameters
        console.log('\n⚡ Generating final SVG (using same parameters)...');
        await parseImage(imageName, 5, preview.colors, { quality: 'fast', maxSize: 600 });
        console.log(`✅ Final SVG generated: ${imageName}.svg`);
        
        // Convert final SVG to PNG for comparison
        console.log('\n🔄 Converting final SVG to PNG for comparison...');
        const svgContent = await fs.readFile(`./${imageName}.svg`, 'utf8');
        const svgBuffer = Buffer.from(svgContent);
        const finalPngPath = `./${imageName}-final-comparison.png`;
        
        await sharp(svgBuffer)
            .png()
            .resize(400, 400, { fit: 'inside', withoutEnlargement: true })
            .toFile(finalPngPath);
        
        console.log(`✅ Final PNG generated: ${finalPngPath}`);
        
        console.log('\n🎯 Consistency Test Complete!');
        console.log('📋 Compare these files:');
        console.log(`   Preview: ${preview.previewPath}`);
        console.log(`   Final:   ${finalPngPath}`);
        console.log('\n✅ They should look identical (same colors, same placement)');
        console.log('✅ If they match, the preview system is working correctly!');
        
    } catch (error) {
        console.error('❌ Error during consistency test:', error.message);
        console.error(error.stack);
    }
}

// Run the test
testPreviewConsistency();
