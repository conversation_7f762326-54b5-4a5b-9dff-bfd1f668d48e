import potrace from "potrace";
import fs from "fs-extra";
import sharp from "sharp";
import tinycolor from "tinycolor2";
import quantize from "quantize";
import SVGO from "svgo";
import NearestColor from "nearest-color";
import replaceAll from "string.prototype.replaceall";
import getColors from 'get-image-colors';

replaceAll.shim();

// https://stackoverflow.com/a/39077686
const hexToRgb = (hex) =>
  hex
    .replace(
      /^#?([a-f\d])([a-f\d])([a-f\d])$/i,
      (m, r, g, b) => "#" + r + r + g + g + b + b
    )
    .substring(1)
    .match(/.{2}/g)
    .map((x) => parseInt(x, 16));

// https://stackoverflow.com/a/35663683
function hexify(color) {
  var values = color
    .replace(/rgba?\(/, "")
    .replace(/\)/, "")
    .replace(/[\s+]/g, "")
    .split(",");
  var a = parseFloat(values[3] || 1),
    r = Math.floor(a * parseInt(values[0]) + (1 - a) * 255),
    g = Math.floor(a * parseInt(values[1]) + (1 - a) * 255),
    b = Math.floor(a * parseInt(values[2]) + (1 - a) * 255);
  return (
    "#" +
    ("0" + r.toString(16)).slice(-2) +
    ("0" + g.toString(16)).slice(-2) +
    ("0" + b.toString(16)).slice(-2)
  );
}

// https://graphicdesign.stackexchange.com/a/91018
function combineOpacity(a, b) {
  return 1 - (1 - a) * (1 - b);
}

function getSolid(svg, stroke) {
  svg = svg.replaceAll(`fill="black"`, "");
  const opacityRegex = /fill-opacity="[\d\.]+"/gi;
  const numberRegex = /[\d\.]+/;
  const matches = svg.match(opacityRegex);
  const colors = Array.from(new Set(matches))
    .map((fillOpacity) => ({
      fillOpacity,
      opacity: Number(fillOpacity.match(numberRegex)[0]),
    }))
    .sort((a, b) => b.opacity - a.opacity)
    .map(({ fillOpacity, opacity }, index, array) => {
      // combine all lighter opacities into dark opacity
      const lighterColors = array.slice(index);
      const trueOpacity = lighterColors.reduce(
        (acc, cur) => combineOpacity(acc, cur.opacity),
        0
      );
      // turn opacity into hex
      const hex = hexify(`rgba(0, 0, 0, ${trueOpacity})`);
      return {
        trueOpacity,
        fillOpacity,
        opacity,
        hex,
      };
    });
  for (const color of colors) {
    if(stroke){
      svg = svg.replaceAll(color.fillOpacity, `fill="${color.hex}" stroke-width="1" stroke="${color.hex}"`);
      svg = svg.replaceAll(` stroke="none"`, "");
    }else{
      svg = svg.replaceAll(color.fillOpacity, `fill="${color.hex}"`);
      svg = svg.replaceAll(` stroke="none"`, "");
    }
  }
  return svg;
}

async function getPixels(input) {
  const image = sharp(input);
  const metadata = await image.metadata();
  const raw = await image.raw().toBuffer();

  const pixels = [];
  for (let i = 0; i < raw.length; i = i + metadata.channels) {
    const pixel = [];
    for (let j = 0; j < metadata.channels; j++) {
      pixel.push(raw.readUInt8(i + j));
    }
    pixels.push(pixel);
  }
  return { pixels, ...metadata };
}

async function replaceColors(svg, original) {
  // if greyscale image, return greyscale svg
  if ((await (await sharp(original).metadata()).channels) === 1) {
    return svg;
  }

  const hexRegex = /#([a-f0-9]{3}){1,2}\b/gi;
  const matches = svg.match(hexRegex);
  const colors = Array.from(new Set(matches));
  const pixelIndexesOfNearestColors = {}; // hex: [array of pixel indexes]
  colors.forEach((color) => (pixelIndexesOfNearestColors[color] = []));

  const svgPixels = await getPixels(Buffer.from(svg));

  const nearestColor = NearestColor.from(colors);

  svgPixels.pixels.forEach((pixel, index) => {
    // curly braces for scope https://stackoverflow.com/a/49350263
    switch (svgPixels.channels) {
      case 3: {
        const [r, g, b] = pixel;
        const rgb = `rgb(${r}, ${g}, ${b})`;
        const hex = hexify(rgb);
        pixelIndexesOfNearestColors[nearestColor(hex)].push(index);
        break;
      }
      case 4: {
        const [r, g, b, a] = pixel;
        const rgba = `rgba(${r}, ${g}, ${b}, ${a / 255})`;
        const hex = hexify(rgba);
        pixelIndexesOfNearestColors[nearestColor(hex)].push(index);
        break;
      }
      default:
        throw new Error("Unsupported number of channels");
    }
  });

  const originalPixels = await getPixels(original);
  const pixelsOfNearestColors = pixelIndexesOfNearestColors;
  Object.keys(pixelsOfNearestColors).forEach((hexKey) => {
    pixelsOfNearestColors[hexKey] = pixelsOfNearestColors[hexKey].map(
      (pixelIndex) => {
        const pixel = originalPixels.pixels[pixelIndex];
        switch (originalPixels.channels) {
          case 3: {
            const [r, g, b] = pixel;
            const rgb = `rgb(${r}, ${g}, ${b})`;
            return hexify(rgb);
          }
          case 4: {
            const [r, g, b, a] = pixel;
            const rgba = `rgba(${r}, ${g}, ${b}, ${a / 255})`;
            return hexify(rgba);
          }
          default:
            throw new Error("Unsupported number of channels");
        }
      }
    );
  });

  const colorsToReplace = pixelsOfNearestColors;
  // FIXED: Use proper color quantization that preserves color variety
  Object.keys(pixelsOfNearestColors).forEach((hexKey) => {
    const pixelArray = colorsToReplace[hexKey].map(hexToRgb);
    if (pixelArray.length > 0) {
      // Use fewer quantization colors and pick the most representative one
      const colorMap = quantize(pixelArray, Math.min(5, pixelArray.length));
      if (colorMap && colorMap.palette().length > 0) {
        const [r, g, b] = colorMap.palette()[0];
        const rgb = `rgb(${r}, ${g}, ${b})`;
        colorsToReplace[hexKey] = hexify(rgb);
      }
    }
  });

  Object.entries(colorsToReplace).forEach(([oldColor, newColor]) => {
    svg = svg.replaceAll(oldColor, newColor);
  });

  return svg;
}

function viewBoxify(svg){
  let width = svg.split('width="')[1].split('"')[0];
  let height = svg.split('height="')[1].split('"')[0];

  let originalHeader = `<svg xmlns="http://www.w3.org/2000/svg" width="${width}" height="${height}">`;
  return svg.replace(originalHeader, `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 ${width} ${height}">`);
}

async function parseImage(imageName, step, colors, options = {}) {

  // Use EXACT original working logic - no modifications for steps 1-4
  if (step <= 4 || !options || Object.keys(options).length === 0) {
    // ORIGINAL WORKING CODE - unchanged
    let svg = await new Promise((resolve, reject) => {
      potrace.posterize(
        "./"+imageName+".png",
        {
          // EXACT original parameters
          optTolerance: 0.5,
          steps: step
        },
        function (err, svg) {
          if (err) return reject(err);
          resolve(svg);
        }
      );
    });

    svg = getSolid(svg, step != 1);

    if(step == 1){
      let paths = svg.split("<path");
      svg = paths[0]+"<path"+paths[2];
      let color = svg.split('#')[1].split('"')[0];
      svg = svg.replaceAll("#"+color, colors[0]);
    }else{
      svg = await replaceColors(svg, await fs.readFile("./"+imageName+".png"));
    }

    svg = (await SVGO.optimize(svg)).data;
    svg = viewBoxify(svg);
    fs.outputFileSync("./"+imageName+".svg", svg);
    console.log("done");
    return;
  }

  // Performance optimization settings ONLY for 5+ colors
  const {
    quality = 'balanced',
    maxSize = null,
    optTolerance = null
  } = options;

  // Resize image if it's too large and maxSize is specified
  let processImagePath = "./"+imageName+".png";
  if (maxSize) {
    const resizedPath = "./"+imageName+"_resized.png";
    const image = sharp(processImagePath);
    const metadata = await image.metadata();

    if (metadata.width > maxSize || metadata.height > maxSize) {
      await image
        .resize(maxSize, maxSize, {
          fit: 'inside',
          withoutEnlargement: true
        })
        .png()
        .toFile(resizedPath);
      processImagePath = resizedPath;
      console.log(`Resized image to max ${maxSize}px for faster processing`);
    }
  }

  // For 5+ colors, use optimized settings to make them practical
  let potraceOptions = {
    steps: step,
    // Progressive tolerance: higher steps need higher tolerance for reasonable speed
    optTolerance: optTolerance || (
      quality === 'ultra_fast' ? 3.0 :  // ULTRA fast for preview
      step >= 12 ? 1.5 :  // Very high tolerance for 12+ colors
      step >= 8 ? 1.2 :   // High tolerance for 8+ colors
      step >= 6 ? 1.0 :   // Moderate tolerance for 6+ colors
      0.8                 // Slightly higher than original for 5 colors
    )
  };

  let svg = await new Promise((resolve, reject) => {
    potrace.posterize(
      processImagePath,
      potraceOptions,
      function (err, svg) {
        if (err) return reject(err);
        resolve(svg);
      }
    );
  });

  svg = getSolid(svg, step != 1);

  if(step == 1){
    let paths = svg.split("<path");
    svg = paths[0]+"<path"+paths[2];
    let color = svg.split('#')[1].split('"')[0];
    svg = svg.replaceAll("#"+color, colors[0]);
  }else{
    // CRITICAL FIX: Use the same image file that Potrace processed
    svg = await replaceColors(svg, await fs.readFile(processImagePath));
  }

  svg = (await SVGO.optimize(svg)).data;

  svg = viewBoxify(svg);

  fs.outputFileSync("./"+imageName+".svg", svg);
  console.log("done");
}

async function inspectImage(imageName, performanceHint = 'balanced'){
  let options = [];

  let listColors = await getColors("./"+imageName+".png", {count: 15});

  let hslList = listColors.map(color => color.hsl());
  let rgbList = listColors.map(color => color.rgb());
  let hexList = listColors.map(color => color.hex());

  let isWhiteBackground = hslList[0][2] > 0.80;
  if(isWhiteBackground){
    hslList = hslList.slice(1);
    rgbList = rgbList.slice(1);
    hexList = hexList.slice(1);
  }

  let isBlackAndWhite = hslList[hslList.length - 1][2] < 0.05;

  if(isNaN(hslList[hslList.length-1][0])){
    isBlackAndWhite = true;
  }

  if(isBlackAndWhite){
    options.push({step: 1, colors: ["#000000"]});

  }else{

    let hueArray = hslList.map((color, i) => {
      if(isNaN(color[0])){
        return 0;
      }else{
        return color[0];
      }
    });

    let lumArray = hslList.map((color, i) => {
      if(isNaN(color[2])){
        return 0;
      }else{
        return color[2];
      }
    });

    let hueDifference = 0;
    let lumDifference = 0;
    for (var i = 0; i < hueArray.length; i++) {
      if(i != 0){
        hueDifference += Math.abs(hueArray[i-1] - hueArray[i]);
        lumDifference += Math.abs(lumArray[i-1] - lumArray[i]);
      }
    }

    let isMonocolor = hueDifference < 5 && lumDifference < 2;

    if(isMonocolor){
      options.push({step: 1, colors: [hexList[hexList.length-1]]});
    }else{
      // Add options based on available colors
      const maxColors = Math.min(hexList.length, 15);
      const stepOptions = [1, 2, 3, 4, 5, 6, 12];

      stepOptions.forEach(step => {
        if (step <= maxColors) {
          // Add performance recommendations
          let recommendedQuality = 'high';
          let estimatedTime = 'Fast';
          let maxSize = null;

          if (step >= 12) {
            recommendedQuality = 'fast';
            estimatedTime = 'Very Slow (5-15 min)';
            maxSize = 600; // Much smaller for 12+ colors
          } else if (step >= 8) {
            recommendedQuality = 'fast';
            estimatedTime = 'Slow (3-8 min)';
            maxSize = 800;
          } else if (step >= 6) {
            recommendedQuality = 'balanced';
            estimatedTime = 'Medium (1-3 min)';
            maxSize = 1000;
          } else if (step >= 5) {
            recommendedQuality = 'balanced';
            estimatedTime = 'Medium (30s-2min)';
            maxSize = 1200;
          } else {
            recommendedQuality = 'high';
            estimatedTime = 'Fast (5-30s)';
          }

          options.push({
            step: step,
            colors: hexList.slice(0, step),
            recommendedQuality: recommendedQuality,
            estimatedTime: estimatedTime,
            maxSize: maxSize
          });
        }
      });
    }

  }

  return options;

}

// Generate color preview using EXACT same process as final vectorization
async function generateColorPreview(imageName, step, colors, shuffleSeed = 0) {
  try {
    // Create unique cache-busting filename
    const timestamp = Date.now();
    const previewPath = `./${imageName}-preview-${step}-${shuffleSeed}-${timestamp}.png`;
    const tempSvgPath = `./${imageName}-preview-temp-${step}-${shuffleSeed}-${timestamp}.svg`;

    // Apply smart color assignment (same as final result)
    const smartColors = createSmartColorPalette(colors, shuffleSeed);

    console.log(`🎨 Generating preview: ${step} colors, shuffle ${shuffleSeed}`);
    console.log(`Smart colors: ${smartColors.join(', ')}`);

    // Use the EXACT same parseImage function as final vectorization
    // but with a temporary filename to avoid conflicts
    const tempImageName = `${imageName}-preview-temp-${timestamp}`;
    const tempImagePath = `./${tempImageName}.png`;

    // Copy original image to temp location
    await fs.copy(`./${imageName}.png`, tempImagePath);

    // Generate SVG using exact same process as final result
    // For preview, use ULTRA low-resolution settings for maximum speed
    const previewOptions = {
      quality: 'ultra_fast',
      maxSize: 200,        // VERY small for ultra-fast preview
      optTolerance: 2.0    // Very high tolerance for maximum speed
    };

    await parseImage(tempImageName, step, smartColors, previewOptions);

    // Read the generated SVG
    const svgContent = await fs.readFile(`./${tempImageName}.svg`, 'utf8');

    // Convert SVG to PNG for preview display
    const svgBuffer = Buffer.from(svgContent);
    await sharp(svgBuffer)
      .png()
      .resize(400, 400, { fit: 'inside', withoutEnlargement: true })
      .toFile(previewPath);

    // Clean up temp files
    await fs.remove(tempImagePath);
    await fs.remove(`./${tempImageName}.svg`);

    return {
      success: true,
      previewPath: previewPath,
      colors: smartColors,
      step: step,
      shuffleSeed: shuffleSeed,
      colorAssignment: {
        darkest: smartColors[0],
        lightest: smartColors[smartColors.length - 1],
        middle: smartColors.slice(1, -1)
      }
    };

  } catch (error) {
    console.error('Error generating color preview:', error);
    throw error;
  }
}

// Modified color replacement function that uses smart color assignment
async function replaceColorsWithPalette(svg, original, targetColors) {
  // if greyscale image, return greyscale svg
  if ((await (await sharp(original).metadata()).channels) === 1) {
    return svg;
  }

  const hexRegex = /#([a-f0-9]{3}){1,2}\b/gi;
  const matches = svg.match(hexRegex);
  const svgColors = Array.from(new Set(matches));

  // Analyze SVG colors by lightness and create smart mapping
  const svgColorsWithLightness = svgColors.map(color => {
    const hsl = tinycolor(color).toHsl();
    return {
      hex: color,
      lightness: hsl.l
    };
  });

  // Sort SVG colors by lightness (darkest to lightest)
  svgColorsWithLightness.sort((a, b) => a.lightness - b.lightness);

  // Create smart color palette (same logic as preview)
  const smartTargetColors = createSmartColorPalette(targetColors, 0); // No shuffle for final result

  // Map SVG colors to smart target colors based on lightness
  const colorMapping = {};
  svgColorsWithLightness.forEach((svgColor, index) => {
    const targetIndex = Math.min(index, smartTargetColors.length - 1);
    colorMapping[svgColor.hex] = smartTargetColors[targetIndex];
  });

  // Apply color replacements
  Object.entries(colorMapping).forEach(([oldColor, newColor]) => {
    svg = svg.replaceAll(oldColor, newColor);
  });

  return svg;
}

// Smart color assignment that keeps light/dark colors fixed and shuffles middle colors
function createSmartColorPalette(colors, shuffleSeed = 0) {
  if (colors.length <= 2) {
    return colors; // Not enough colors to shuffle
  }

  // Convert hex colors to HSL to analyze lightness
  const colorsWithLightness = colors.map(color => {
    const hsl = tinycolor(color).toHsl();
    return {
      hex: color,
      lightness: hsl.l,
      saturation: hsl.s,
      hue: hsl.h
    };
  });

  // Sort by lightness
  colorsWithLightness.sort((a, b) => a.lightness - b.lightness);

  // Fixed assignments
  const darkestColor = colorsWithLightness[0].hex;  // Darkest (shadows, mouth, etc.)
  const lightestColor = colorsWithLightness[colorsWithLightness.length - 1].hex; // Lightest (eyes, teeth, etc.)

  // Middle colors that can be shuffled
  const middleColors = colorsWithLightness.slice(1, -1).map(c => c.hex);

  // Shuffle only the middle colors
  if (shuffleSeed > 0 && middleColors.length > 1) {
    // Deterministic shuffle of middle colors only
    let shuffledMiddle = [...middleColors];
    for (let i = 0; i < shuffleSeed; i++) {
      const temp = shuffledMiddle.shift();
      shuffledMiddle.push(temp);
    }
    middleColors.splice(0, middleColors.length, ...shuffledMiddle);
  }

  // Reconstruct the palette: darkest + shuffled middle + lightest
  const smartPalette = [darkestColor, ...middleColors, lightestColor];

  return smartPalette;
}

// Alternative simpler preview method using direct color quantization with smart color assignment
async function generateSimpleColorPreview(imageName, step, colors, shuffleSeed = 0) {
  try {
    // Create unique cache-busting filename
    const timestamp = Date.now();
    const inputPath = `./${imageName}.png`;
    const previewPath = `./${imageName}-simple-preview-${step}-${shuffleSeed}-${timestamp}.png`;

    // Create smart color palette with fixed light/dark colors
    const smartColors = createSmartColorPalette(colors, shuffleSeed);

    console.log(`🎨 Generating simple preview: ${step} colors, shuffle ${shuffleSeed}`);
    console.log(`Smart colors: ${smartColors.join(', ')}`);

    // Read and process the image
    const image = sharp(inputPath);
    const { width, height } = await image.metadata();

    // Resize for preview
    const maxSize = 400;
    let processedImage = image;
    if (width > maxSize || height > maxSize) {
      processedImage = processedImage.resize(maxSize, maxSize, {
        fit: 'inside',
        withoutEnlargement: true
      });
    }

    // Get raw pixel data
    const { data, info } = await processedImage
      .raw()
      .toBuffer({ resolveWithObject: true });

    // Create a simple color quantization
    const pixels = [];
    for (let i = 0; i < data.length; i += info.channels) {
      pixels.push([data[i], data[i + 1], data[i + 2]]);
    }

    // Use quantize to reduce to the target number of colors
    const colorMap = quantize(pixels, step);
    const palette = colorMap ? colorMap.palette() : [];

    // Smart mapping: assign colors based on lightness
    const quantizedData = Buffer.alloc(data.length);

    if (palette.length > 0) {
      // Analyze palette lightness and create smart mapping
      const paletteWithLightness = palette.map((color, index) => {
        const [r, g, b] = color;
        const lightness = (0.299 * r + 0.587 * g + 0.114 * b) / 255; // Perceived lightness
        return { color, lightness, index };
      });

      // Sort palette by lightness
      paletteWithLightness.sort((a, b) => a.lightness - b.lightness);

      // Create mapping: darkest palette color → darkest target color, etc.
      const colorMapping = {};
      paletteWithLightness.forEach((paletteColor, index) => {
        const targetIndex = Math.min(index, smartColors.length - 1);
        const key = `${paletteColor.color[0]},${paletteColor.color[1]},${paletteColor.color[2]}`;
        colorMapping[key] = smartColors[targetIndex];
      });

      // Apply the smart color mapping
      for (let i = 0; i < data.length; i += info.channels) {
        const pixel = [data[i], data[i + 1], data[i + 2]];
        const quantizedColor = colorMap.map(pixel);

        const key = `${quantizedColor[0]},${quantizedColor[1]},${quantizedColor[2]}`;
        const targetColor = colorMapping[key] || smartColors[0];

        // Convert hex to RGB
        const rgb = hexToRgb(targetColor);
        quantizedData[i] = rgb[0];     // R
        quantizedData[i + 1] = rgb[1]; // G
        quantizedData[i + 2] = rgb[2]; // B
        if (info.channels === 4) {
          quantizedData[i + 3] = data[i + 3]; // A (preserve alpha)
        }
      }
    } else {
      // Fallback if quantization fails
      for (let i = 0; i < data.length; i += info.channels) {
        const rgb = hexToRgb(smartColors[0]);
        quantizedData[i] = rgb[0];
        quantizedData[i + 1] = rgb[1];
        quantizedData[i + 2] = rgb[2];
        if (info.channels === 4) {
          quantizedData[i + 3] = data[i + 3];
        }
      }
    }

    // Create the preview image
    await sharp(quantizedData, {
      raw: {
        width: info.width,
        height: info.height,
        channels: info.channels
      }
    })
    .png()
    .toFile(previewPath);

    return {
      success: true,
      previewPath: previewPath,
      colors: smartColors,
      step: step,
      shuffleSeed: shuffleSeed,
      colorAssignment: {
        darkest: smartColors[0],
        lightest: smartColors[smartColors.length - 1],
        middle: smartColors.slice(1, -1)
      }
    };

  } catch (error) {
    console.error('Error generating simple color preview:', error);
    throw error;
  }
}

export  { inspectImage, parseImage, generateColorPreview, generateSimpleColorPreview, createSmartColorPalette };
