script.js:47 🔍 Preview button clicked
script.js:254 🔍 showPreview called
script.js:255 Selected option: {step: 5, colors: Array(5), recommendedQuality: 'balanced', estimatedTime: 'Medium (30s-2min)', maxSize: 1200}
script.js:256 Current filename: image-1751798935552-417149895
script.js:264 🔍 Starting preview generation...
script.js:319 🔍 Debug: Preview result received
script.js:320 Preview data length: 67158
script.js:321 Preview starts with: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAANYA
script.js:322 Color preview element: <img id=​"colorPreview" class=​"preview-image" alt=​"Color Preview" src=​"data:​image/​png;​base64,iVBOR…​y4VNBc/​ZrTfAAAAAElFTkSuQmCC" style=​"opacity:​ 1;​ filter:​ none;​">​
script.js:354 ✅ Preview updated: 5 colors, shuffle 0
script.js:355 Colors: #333c38, #b3502b, #d38c4b, #dca47a, #93d1c4
script.js:344 ❌ Preview image failed to load
colorPreview.onerror @ script.js:344
script.js:330 🔍 Setting preview src...
script.js:332 🔍 Preview src set to: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAANYA
script.js:337 🔍 Preview image loaded successfully
script.js:319 🔍 Debug: Preview result received
script.js:320 Preview data length: 67158
script.js:321 Preview starts with: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAANYA
script.js:322 Color preview element: <img id=​"colorPreview" class=​"preview-image" alt=​"Color Preview" src=​"data:​image/​png;​base64,iVBOR…​y4VNBc/​ZrTfAAAAAElFTkSuQmCC" style=​"opacity:​ 0.5;​ filter:​ blur(2px)​;​">​
script.js:354 ✅ Preview updated: 5 colors, shuffle 1
script.js:355 Colors: #333c38, #d38c4b, #dca47a, #b3502b, #93d1c4
script.js:344 ❌ Preview image failed to load
colorPreview.onerror @ script.js:344
script.js:330 🔍 Setting preview src...
script.js:332 🔍 Preview src set to: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAANYA
script.js:337 🔍 Preview image loaded successfully
script.js:319 🔍 Debug: Preview result received
script.js:320 Preview data length: 67158
script.js:321 Preview starts with: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAANYA
script.js:322 Color preview element: <img id=​"colorPreview" class=​"preview-image" alt=​"Color Preview" src=​"data:​image/​png;​base64,iVBOR…​y4VNBc/​ZrTfAAAAAElFTkSuQmCC" style=​"opacity:​ 0.5;​ filter:​ blur(2px)​;​">​
script.js:354 ✅ Preview updated: 5 colors, shuffle 2
script.js:355 Colors: #333c38, #dca47a, #b3502b, #d38c4b, #93d1c4
script.js:344 ❌ Preview image failed to load
colorPreview.onerror @ script.js:344
script.js:330 🔍 Setting preview src...
script.js:332 🔍 Preview src set to: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAANYA
script.js:337 🔍 Preview image loaded successfully
