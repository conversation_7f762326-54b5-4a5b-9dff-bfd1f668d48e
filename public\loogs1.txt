script.js:47 🔍 Preview button clicked
script.js:254 🔍 showPreview called
script.js:255 Selected option: {step: 2, colors: Array(2), recommendedQuality: 'high', estimatedTime: 'Fast (5-30s)', maxSize: null}colors: (2) ['#333c38', '#d38c4b']estimatedTime: "Fast (5-30s)"maxSize: nullrecommendedQuality: "high"step: 2[[Prototype]]: Object
script.js:256 Current filename: image-1751803484797-191761681
script.js:264 🔍 Starting preview generation...
script.js:316 🚀 Generating ULTRA-FAST preview: 2 colors
script.js:317 Request payload: {filename: 'image-1751803484797-191761681', step: 2, colors: Array(2), shuffleSeed: 0, method: 'vectorized'}
script.js:332 📡 Sending fetch request...
script.js:353 📡 Response received in 859ms
script.js:354 Response status: 200
script.js:355 Response ok: true
script.js:360 🔍 Debug: Preview result received
script.js:361 Preview data length: 45294
script.js:362 Preview starts with: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAANYA
script.js:363 Color preview element: <img id=​"colorPreview" class=​"preview-image" alt=​"Color Preview" src style=​"opacity:​ 0.5;​ filter:​ blur(2px)​;​">​
script.js:395 ✅ Preview updated: 2 colors, shuffle 0
script.js:396 Colors: #333c38, #d38c4b
script.js:373 ❌ Preview image failed to load: Event {isTrusted: true, type: 'error', target: img#colorPreview.preview-image, currentTarget: img#colorPreview.preview-image, eventPhase: 2, …}
colorPreview.onerror @ script.js:373
script.js:374 Image src length: 22
colorPreview.onerror @ script.js:374
script.js:375 Image src start: http://localhost:3000/
colorPreview.onerror @ script.js:375
script.js:385 🔍 Setting preview src...
script.js:387 🔍 Preview src set to: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAANYA
script.js:367 🔍 Preview image loaded successfully
script.js:316 🚀 Generating ULTRA-FAST preview: 2 colors
script.js:317 Request payload: {filename: 'image-1751803484797-191761681', step: 2, colors: Array(2), shuffleSeed: 1, method: 'vectorized'}
script.js:332 📡 Sending fetch request...
script.js:353 📡 Response received in 673ms
script.js:354 Response status: 200
script.js:355 Response ok: true
script.js:360 🔍 Debug: Preview result received
script.js:361 Preview data length: 45294
script.js:362 Preview starts with: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAANYA
script.js:363 Color preview element: <img id=​"colorPreview" class=​"preview-image" alt=​"Color Preview" src=​"data:​image/​png;​base64,iVBOR…1+1nE4/​JcikAAAAASUVORK5CYII=" style=​"opacity:​ 1;​ filter:​ none;​">​
script.js:395 ✅ Preview updated: 2 colors, shuffle 1
script.js:396 Colors: #333c38, #d38c4b
script.js:373 ❌ Preview image failed to load: Event {isTrusted: true, type: 'error', target: img#colorPreview.preview-image, currentTarget: img#colorPreview.preview-image, eventPhase: 2, …}
colorPreview.onerror @ script.js:373
script.js:374 Image src length: 22
colorPreview.onerror @ script.js:374
script.js:375 Image src start: http://localhost:3000/
colorPreview.onerror @ script.js:375
script.js:385 🔍 Setting preview src...
script.js:387 🔍 Preview src set to: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAANYA
script.js:367 🔍 Preview image loaded successfully
script.js:316 🚀 Generating ULTRA-FAST preview: 2 colors
script.js:317 Request payload: {filename: 'image-1751803484797-191761681', step: 2, colors: Array(2), shuffleSeed: 2, method: 'vectorized'}
script.js:332 📡 Sending fetch request...
script.js:353 📡 Response received in 519ms
script.js:354 Response status: 200
script.js:355 Response ok: true
script.js:360 🔍 Debug: Preview result received
script.js:361 Preview data length: 45294
script.js:362 Preview starts with: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAANYA
script.js:363 Color preview element: <img id=​"colorPreview" class=​"preview-image" alt=​"Color Preview" src style=​"opacity:​ 0.5;​ filter:​ blur(2px)​;​">​
script.js:395 ✅ Preview updated: 2 colors, shuffle 2
script.js:396 Colors: #333c38, #d38c4b
script.js:373 ❌ Preview image failed to load: Event {isTrusted: true, type: 'error', target: img#colorPreview.preview-image, currentTarget: img#colorPreview.preview-image, eventPhase: 2, …}
colorPreview.onerror @ script.js:373
script.js:374 Image src length: 22
colorPreview.onerror @ script.js:374
script.js:375 Image src start: http://localhost:3000/
colorPreview.onerror @ script.js:375
script.js:385 🔍 Setting preview src...
script.js:387 🔍 Preview src set to: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAANYA
script.js:367 🔍 Preview image loaded successfully
script.js:47 🔍 Preview button clicked
script.js:254 🔍 showPreview called
script.js:255 Selected option: {step: 3, colors: Array(3), recommendedQuality: 'high', estimatedTime: 'Fast (5-30s)', maxSize: null}
script.js:256 Current filename: image-1751803484797-191761681
script.js:264 🔍 Starting preview generation...
script.js:316 🚀 Generating ULTRA-FAST preview: 3 colors
script.js:317 Request payload: {filename: 'image-1751803484797-191761681', step: 3, colors: Array(3), shuffleSeed: 0, method: 'vectorized'}
script.js:332 📡 Sending fetch request...
script.js:353 📡 Response received in 596ms
script.js:354 Response status: 200
script.js:355 Response ok: true
script.js:360 🔍 Debug: Preview result received
script.js:361 Preview data length: 53002
script.js:362 Preview starts with: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAANYA
script.js:363 Color preview element: <img id=​"colorPreview" class=​"preview-image" alt=​"Color Preview" src style=​"opacity:​ 0.5;​ filter:​ blur(2px)​;​">​
script.js:395 ✅ Preview updated: 3 colors, shuffle 0
script.js:396 Colors: #333c38, #d38c4b, #93d1c4
script.js:373 ❌ Preview image failed to load: Event {isTrusted: true, type: 'error', target: img#colorPreview.preview-image, currentTarget: img#colorPreview.preview-image, eventPhase: 2, …}
colorPreview.onerror @ script.js:373
script.js:374 Image src length: 22
colorPreview.onerror @ script.js:374
script.js:375 Image src start: http://localhost:3000/
colorPreview.onerror @ script.js:375
script.js:385 🔍 Setting preview src...
script.js:387 🔍 Preview src set to: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAANYA
script.js:367 🔍 Preview image loaded successfully
script.js:316 🚀 Generating ULTRA-FAST preview: 3 colors
script.js:317 Request payload: {filename: 'image-1751803484797-191761681', step: 3, colors: Array(3), shuffleSeed: 1, method: 'vectorized'}
script.js:332 📡 Sending fetch request...
script.js:353 📡 Response received in 562ms
script.js:354 Response status: 200
script.js:355 Response ok: true
script.js:360 🔍 Debug: Preview result received
script.js:361 Preview data length: 53002
script.js:362 Preview starts with: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAANYA
script.js:363 Color preview element: <img id=​"colorPreview" class=​"preview-image" alt=​"Color Preview" src=​"data:​image/​png;​base64,iVBOR…+f8xIKSj1EB6uAAAAAElFTkSuQmCC" style=​"opacity:​ 0.5;​ filter:​ blur(2px)​;​">​
script.js:395 ✅ Preview updated: 3 colors, shuffle 1
script.js:396 Colors: #333c38, #d38c4b, #93d1c4
script.js:373 ❌ Preview image failed to load: Event {isTrusted: true, type: 'error', target: img#colorPreview.preview-image, currentTarget: img#colorPreview.preview-image, eventPhase: 2, …}
colorPreview.onerror @ script.js:373
script.js:374 Image src length: 22
colorPreview.onerror @ script.js:374
script.js:375 Image src start: http://localhost:3000/
colorPreview.onerror @ script.js:375
script.js:385 🔍 Setting preview src...
script.js:387 🔍 Preview src set to: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAANYA
script.js:367 🔍 Preview image loaded successfully
script.js:47 🔍 Preview button clicked
script.js:254 🔍 showPreview called
script.js:255 Selected option: {step: 6, colors: Array(6), recommendedQuality: 'balanced', estimatedTime: 'Medium (1-3 min)', maxSize: 1000}
script.js:256 Current filename: image-1751803484797-191761681
script.js:264 🔍 Starting preview generation...
script.js:316 🚀 Generating ULTRA-FAST preview: 6 colors
script.js:317 Request payload: {filename: 'image-1751803484797-191761681', step: 6, colors: Array(6), shuffleSeed: 0, method: 'vectorized'}
script.js:332 📡 Sending fetch request...
script.js:328 ❌ Request timeout after 60 seconds
(anonymous) @ script.js:328
setTimeout
generateColorPreview @ script.js:327
showPreview @ script.js:278
(anonymous) @ script.js:48
