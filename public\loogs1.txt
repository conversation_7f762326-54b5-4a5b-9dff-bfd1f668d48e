script.js:47 🔍 Preview button clicked
script.js:254 🔍 showPreview called
script.js:255 Selected option: Object
script.js:256 Current filename: image-1751800533906-134503644
script.js:264 🔍 Starting preview generation...
script.js:319 🔍 Debug: Preview result received
script.js:320 Preview data length: 60242
script.js:321 Preview starts with: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAANYA
script.js:322 Color preview element: <img id=​"colorPreview" class=​"preview-image" alt=​"Color Preview" src=​"data:​image/​png;​base64,iVBOR…DpF4Nd+jClU0AAAAASUVORK5CYII=" style=​"opacity:​ 1;​ filter:​ none;​">​
script.js:352 ✅ Preview updated: 4 colors, shuffle 0
script.js:353 Colors: #333c38, #b3502b, #d38c4b, #93d1c4
script.js:332 ❌ Preview image failed to load
colorPreview.onerror @ script.js:332
script.js:342 🔍 Setting preview src...
script.js:344 🔍 Preview src set to: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAANYA
script.js:326 🔍 Preview image loaded successfully
script.js:319 🔍 Debug: Preview result received
script.js:320 Preview data length: 60242
script.js:321 Preview starts with: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAANYA
script.js:322 Color preview element: <img id=​"colorPreview" class=​"preview-image" alt=​"Color Preview" src=​"data:​image/​png;​base64,iVBOR…DpF4Nd+jClU0AAAAASUVORK5CYII=" style=​"opacity:​ 1;​ filter:​ none;​">​
script.js:352 ✅ Preview updated: 4 colors, shuffle 1
script.js:353 Colors: #333c38, #d38c4b, #b3502b, #93d1c4
script.js:332 ❌ Preview image failed to load
colorPreview.onerror @ script.js:332
script.js:342 🔍 Setting preview src...
script.js:344 🔍 Preview src set to: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAANYA
script.js:326 🔍 Preview image loaded successfully
script.js:47 🔍 Preview button clicked
script.js:254 🔍 showPreview called
script.js:255 Selected option: {step: 6, colors: Array(6), recommendedQuality: 'balanced', estimatedTime: 'Medium (1-3 min)', maxSize: 1000}
script.js:256 Current filename: image-1751800533906-134503644
script.js:264 🔍 Starting preview generation...
